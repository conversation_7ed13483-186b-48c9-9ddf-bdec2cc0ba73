/**
 * Voice Settings Panel - Advanced Voice Configuration
 * Natural human-like speech settings superior to ChatGPT Pro
 */

class VoiceSettings {
    constructor() {
        this.isVisible = false;
        this.currentSettings = {
            naturalSpeech: true,
            emotionalIntonation: true,
            contextualAdaptation: true,
            voiceProfile: 'professional_arabic',
            dialect: 'standard',
            speechRate: 1.0,
            speechPitch: 1.0,
            speechVolume: 1.0
        };
    }

    // Show voice settings panel
    show() {
        if (this.isVisible) return;

        this.createSettingsPanel();
        this.isVisible = true;
    }

    // Hide voice settings panel
    hide() {
        const panel = document.getElementById('voiceSettingsPanel');
        if (panel) {
            panel.remove();
        }
        this.isVisible = false;
    }

    // Create settings panel
    createSettingsPanel() {
        const panel = document.createElement('div');
        panel.id = 'voiceSettingsPanel';
        panel.innerHTML = this.generatePanelHTML();
        panel.style.cssText = this.getPanelStyles();

        document.body.appendChild(panel);
        this.attachEventListeners();
        this.loadCurrentSettings();
    }

    // Generate panel HTML
    generatePanelHTML() {
        return `
            <div class="voice-settings-content">
                <div class="voice-settings-header">
                    <h3><i class="fas fa-microphone"></i> إعدادات الصوت المتقدمة</h3>
                    <button class="close-btn" onclick="voiceSettings.hide()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="voice-settings-body">
                    <!-- Dialect Selection -->
                    <div class="setting-group">
                        <label><i class="fas fa-globe"></i> اللهجة:</label>
                        <select id="dialectSelect" class="voice-select">
                            <option value="standard">الفصحى</option>
                            <option value="iraqi">العراقية الأصيلة</option>
                        </select>
                    </div>

                    <!-- Voice Profile Selection -->
                    <div class="setting-group">
                        <label><i class="fas fa-user-tie"></i> نمط الصوت:</label>
                        <select id="voiceProfile" class="voice-select">
                            <option value="professional_arabic">احترافي فصحى</option>
                            <option value="casual_arabic">عادي ودود فصحى</option>
                            <option value="technical_expert">خبير تقني فصحى</option>
                            <option value="enthusiastic">متحمس فصحى</option>
                            <option value="iraqi_professional">احترافي عراقي</option>
                            <option value="iraqi_casual">عادي عراقي</option>
                            <option value="iraqi_technical">تقني عراقي</option>
                            <option value="iraqi_enthusiastic">متحمس عراقي</option>
                        </select>
                    </div>

                    <!-- Natural Speech Features -->
                    <div class="setting-group">
                        <label><i class="fas fa-brain"></i> الميزات الذكية:</label>
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="naturalSpeech" checked>
                                <span>كلام طبيعي متقدم</span>
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="emotionalIntonation" checked>
                                <span>نبرة عاطفية ذكية</span>
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="contextualAdaptation" checked>
                                <span>تكيف حسب السياق</span>
                            </label>
                        </div>
                    </div>

                    <!-- Speech Rate -->
                    <div class="setting-group">
                        <label><i class="fas fa-tachometer-alt"></i> سرعة الكلام:</label>
                        <div class="slider-container">
                            <input type="range" id="speechRate" min="0.5" max="2.0" step="0.1" value="1.0">
                            <span class="slider-value" id="rateValue">1.0</span>
                        </div>
                    </div>

                    <!-- Speech Pitch -->
                    <div class="setting-group">
                        <label><i class="fas fa-music"></i> نبرة الصوت:</label>
                        <div class="slider-container">
                            <input type="range" id="speechPitch" min="0.5" max="2.0" step="0.1" value="1.0">
                            <span class="slider-value" id="pitchValue">1.0</span>
                        </div>
                    </div>

                    <!-- Speech Volume -->
                    <div class="setting-group">
                        <label><i class="fas fa-volume-up"></i> مستوى الصوت:</label>
                        <div class="slider-container">
                            <input type="range" id="speechVolume" min="0.1" max="1.0" step="0.1" value="1.0">
                            <span class="slider-value" id="volumeValue">1.0</span>
                        </div>
                    </div>

                    <!-- Test Voice -->
                    <div class="setting-group">
                        <label><i class="fas fa-play"></i> اختبار الصوت:</label>
                        <button class="test-voice-btn" onclick="voiceSettings.testVoice()">
                            <i class="fas fa-play"></i> اختبار الصوت الحالي
                        </button>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <button class="save-btn" onclick="voiceSettings.saveSettings()">
                            <i class="fas fa-save"></i> حفظ الإعدادات
                        </button>
                        <button class="reset-btn" onclick="voiceSettings.resetToDefaults()">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Get panel styles
    getPanelStyles() {
        return `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        `;
    }

    // Attach event listeners
    attachEventListeners() {
        // Dialect change
        document.getElementById('dialectSelect').addEventListener('change', (e) => {
            this.currentSettings.dialect = e.target.value;
            this.updateVoiceProfileOptions(e.target.value);
            this.applySettings();
        });

        // Voice profile change
        document.getElementById('voiceProfile').addEventListener('change', (e) => {
            this.currentSettings.voiceProfile = e.target.value;
            this.applySettings();
        });

        // Checkboxes
        ['naturalSpeech', 'emotionalIntonation', 'contextualAdaptation'].forEach(id => {
            document.getElementById(id).addEventListener('change', (e) => {
                this.currentSettings[id] = e.target.checked;
                this.applySettings();
            });
        });

        // Sliders
        this.attachSliderListener('speechRate', 'rateValue');
        this.attachSliderListener('speechPitch', 'pitchValue');
        this.attachSliderListener('speechVolume', 'volumeValue');

        // Close on background click
        document.getElementById('voiceSettingsPanel').addEventListener('click', (e) => {
            if (e.target.id === 'voiceSettingsPanel') {
                this.hide();
            }
        });
    }

    // Attach slider listener
    attachSliderListener(sliderId, valueId) {
        const slider = document.getElementById(sliderId);
        const valueSpan = document.getElementById(valueId);

        slider.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            valueSpan.textContent = value.toFixed(1);
            this.currentSettings[sliderId] = value;
            this.applySettings();
        });
    }

    // Load current settings
    loadCurrentSettings() {
        // Load from localStorage if available
        const saved = localStorage.getItem('voiceSettings');
        if (saved) {
            this.currentSettings = { ...this.currentSettings, ...JSON.parse(saved) };
        }

        // Apply to UI
        document.getElementById('dialectSelect').value = this.currentSettings.dialect;
        document.getElementById('voiceProfile').value = this.currentSettings.voiceProfile;
        document.getElementById('naturalSpeech').checked = this.currentSettings.naturalSpeech;
        document.getElementById('emotionalIntonation').checked = this.currentSettings.emotionalIntonation;
        document.getElementById('contextualAdaptation').checked = this.currentSettings.contextualAdaptation;
        
        document.getElementById('speechRate').value = this.currentSettings.speechRate;
        document.getElementById('rateValue').textContent = this.currentSettings.speechRate.toFixed(1);
        
        document.getElementById('speechPitch').value = this.currentSettings.speechPitch;
        document.getElementById('pitchValue').textContent = this.currentSettings.speechPitch.toFixed(1);
        
        document.getElementById('speechVolume').value = this.currentSettings.speechVolume;
        document.getElementById('volumeValue').textContent = this.currentSettings.speechVolume.toFixed(1);

        // Update voice profile options based on dialect
        this.updateVoiceProfileOptions(this.currentSettings.dialect);
    }

    // Update voice profile options based on selected dialect
    updateVoiceProfileOptions(dialect) {
        const voiceProfileSelect = document.getElementById('voiceProfile');
        const currentValue = voiceProfileSelect.value;

        // Clear current options
        voiceProfileSelect.innerHTML = '';

        if (dialect === 'iraqi') {
            // Iraqi dialect options
            voiceProfileSelect.innerHTML = `
                <option value="iraqi_professional">احترافي عراقي</option>
                <option value="iraqi_casual">عادي عراقي ودود</option>
                <option value="iraqi_technical">خبير تقني عراقي</option>
                <option value="iraqi_enthusiastic">متحمس عراقي</option>
            `;

            // Set default Iraqi profile if current is not Iraqi
            if (!currentValue.includes('iraqi')) {
                this.currentSettings.voiceProfile = 'iraqi_professional';
                voiceProfileSelect.value = 'iraqi_professional';
            } else {
                voiceProfileSelect.value = currentValue;
            }
        } else {
            // Standard Arabic options
            voiceProfileSelect.innerHTML = `
                <option value="professional_arabic">احترافي فصحى</option>
                <option value="casual_arabic">عادي ودود فصحى</option>
                <option value="technical_expert">خبير تقني فصحى</option>
                <option value="enthusiastic">متحمس فصحى</option>
            `;

            // Set default standard profile if current is Iraqi
            if (currentValue.includes('iraqi')) {
                this.currentSettings.voiceProfile = 'professional_arabic';
                voiceProfileSelect.value = 'professional_arabic';
            } else {
                voiceProfileSelect.value = currentValue;
            }
        }
    }

    // Apply settings to voice engine
    applySettings() {
        if (window.advancedVoiceEngine) {
            // Apply dialect first
            advancedVoiceEngine.setDialect(this.currentSettings.dialect);

            // Apply voice profile
            advancedVoiceEngine.setVoiceProfile(this.currentSettings.voiceProfile);

            // Apply speech settings
            advancedVoiceEngine.speechRate = this.currentSettings.speechRate;
            advancedVoiceEngine.speechPitch = this.currentSettings.speechPitch;
            advancedVoiceEngine.speechVolume = this.currentSettings.speechVolume;

            // Apply natural speech features
            advancedVoiceEngine.naturalPauses = this.currentSettings.naturalSpeech;
            advancedVoiceEngine.emotionalIntonation = this.currentSettings.emotionalIntonation;
            advancedVoiceEngine.contextualAdaptation = this.currentSettings.contextualAdaptation;

            console.log(`🎤 تم تطبيق الإعدادات: ${this.currentSettings.dialect} - ${this.currentSettings.voiceProfile}`);
        }
    }

    // Test voice with current settings
    async testVoice() {
        let testText;

        // اختيار النص حسب اللهجة
        if (this.currentSettings.dialect === 'iraqi') {
            testText = "أهلين وسهلين! هذا اختبار للصوت العراقي الطبيعي المتقدم. أنا أتحدث الآن باللهجة العراقية الأصيلة أفضل من ChatGPT المدفوع. شلون تشوف جودة الصوت؟";
        } else {
            testText = "مرحباً! هذا اختبار للصوت الطبيعي المتقدم. أنا أتحدث الآن بطريقة طبيعية أكثر من ChatGPT المدفوع. كيف تجد جودة الصوت؟";
        }

        if (window.advancedVoiceEngine) {
            await advancedVoiceEngine.speakWithContext(testText, {
                emotion: 'friendly',
                context: 'testing',
                isResponse: true
            });
        } else {
            // Fallback to basic speech
            if (typeof speakText === 'function') {
                speakText(testText);
            }
        }
    }

    // Save settings
    saveSettings() {
        localStorage.setItem('voiceSettings', JSON.stringify(this.currentSettings));
        
        // Show success message
        this.showMessage('تم حفظ الإعدادات بنجاح!', 'success');
        
        // Hide panel after short delay
        setTimeout(() => {
            this.hide();
        }, 1500);
    }

    // Reset to defaults
    resetToDefaults() {
        this.currentSettings = {
            naturalSpeech: true,
            emotionalIntonation: true,
            contextualAdaptation: true,
            voiceProfile: 'professional_arabic',
            dialect: 'standard',
            speechRate: 1.0,
            speechPitch: 1.0,
            speechVolume: 1.0
        };

        this.loadCurrentSettings();
        this.applySettings();
        this.showMessage('تم إعادة تعيين الإعدادات', 'info');
    }

    // Show message
    showMessage(text, type = 'info') {
        const message = document.createElement('div');
        message.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 10001;
            ${type === 'success' ? 'background: #27ae60;' : 'background: #3498db;'}
        `;
        message.textContent = text;
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            message.remove();
        }, 3000);
    }
}

// Create global instance
const voiceSettings = new VoiceSettings();

// Export for global use
if (typeof window !== 'undefined') {
    window.voiceSettings = voiceSettings;
    window.VoiceSettings = VoiceSettings;
}
